[project]
name = "latentsync-fix"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "accelerate==0.26.1",
    "decord==0.6.0",
    "deepcache==0.1.1",
    "diffusers==0.32.2",
    "einops==0.7.0",
    "face-alignment==1.4.1",
    "ffmpeg-python==0.2.0",
    "gradio==5.24.0",
    "huggingface-hub==0.30.2",
    "imageio==2.31.1",
    "imageio-ffmpeg==0.5.1",
    "insightface==0.7.3",
    "kornia==0.8.0",
    "librosa==0.10.1",
    "lpips==0.1.4",
    "mediapipe==0.10.11",
    "numpy==1.26.4",
    "omegaconf==2.3.0",
    "onnxruntime-gpu==1.21.0",
    "opencv-python==********",
    "python-speech-features==0.6",
    "scenedetect==0.6.1",
    "torch==2.5.1",
    "torchvision==0.20.1",
    "transformers==4.48.0",
]
