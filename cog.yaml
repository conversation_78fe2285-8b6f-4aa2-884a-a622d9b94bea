# Configuration for Cog ⚙️
# Reference: https://cog.run/yaml

build:
  gpu: true
  cuda: "12.1"
  system_packages:
    - "ffmpeg"
    - "libgl1"
  python_version: "3.10.13"
  python_requirements: requirements.txt

  run:
    - curl -o /usr/local/bin/pget -L "https://github.com/replicate/pget/releases/download/v0.10.2/pget_linux_x86_64" && chmod +x /usr/local/bin/pget

# predict.py defines how predictions are run on your model
predict: "predict.py:Predictor"
